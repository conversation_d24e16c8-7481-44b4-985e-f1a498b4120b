-- 合并优化后的MySQL查询 - 当期与同期对比分析
WITH 
-- 现金流数据CTE：同时计算当期和同期数据
cash_data AS (
    SELECT 
        a.office_name,
        -- 当期数据
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
                 AND a.billing_method_groupCode = '现款支付' THEN a.amount ELSE 0 END) AS xkzf_curr,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
                 AND a.billing_method_groupCode = '资产抵扣' THEN a.amount ELSE 0 END) AS zcdk_curr,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
                 AND a.billing_method_groupCode = '权益折让' THEN a.amount ELSE 0 END) AS qyzr_curr,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
                 AND a.billing_method_groupCode = '平台结算' THEN a.amount ELSE 0 END) AS ptjs_curr,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
                 AND a.billing_method_groupCode IN ('现款支付','资产抵扣','平台结算') THEN a.amount ELSE 0 END) AS sjskje_curr,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}' THEN a.amount ELSE 0 END) AS hjskje_curr,
        
        -- 同期数据（上月同期）
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 AND a.billing_method_groupCode = '现款支付' THEN a.amount ELSE 0 END) AS xkzf_prev,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 AND a.billing_method_groupCode = '资产抵扣' THEN a.amount ELSE 0 END) AS zcdk_prev,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 AND a.billing_method_groupCode = '权益折让' THEN a.amount ELSE 0 END) AS qyzr_prev,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 AND a.billing_method_groupCode = '平台结算' THEN a.amount ELSE 0 END) AS ptjs_prev,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 AND a.billing_method_groupCode IN ('现款支付','资产抵扣','平台结算') THEN a.amount ELSE 0 END) AS sjskje_prev,
        SUM(CASE WHEN DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH) THEN a.amount ELSE 0 END) AS hjskje_prev
    FROM (
        SELECT 
            a.office_name,
            a.customer_id,
            a.membership_number,
            b.level_name,
            a.customer_name,
            c.order_number,
            a.category_level1_name,
            a.category_level2_name,
            a.category_level3_name,
            a.envelope_name,
            a.product_name,
            a.business_date,
            a.billing_method_groupCode,
            a.order_type,
            a.order_id,
            a.order_item_id,
            a.order_line_index,
            c.unit_retail_price,
            c.order_quantity,
            SUM(a.amount) AS amount,
            -- 计算订单预计金额
            CASE WHEN a.order_type = '退款' 
                 THEN -c.order_quantity * c.unit_retail_price  
                 ELSE c.order_quantity * c.unit_retail_price 
            END AS ddyj,
            -- 计算折扣比例
            ROUND(COALESCE(SUM(a.amount) * 100.0 / NULLIF(
                CASE WHEN a.order_type = '退款' 
                     THEN -c.order_quantity * c.unit_retail_price  
                     ELSE c.order_quantity * c.unit_retail_price 
                END, 0), 0), 1) AS zkbl
        FROM temp_middle_cash a 
        LEFT JOIN dim_customer_irn_dd b ON a.customer_id = b.customer_id
        LEFT JOIN dws_trd_order_item_irn_dd c ON a.order_item_id = c.order_item_id
        WHERE (
            DATE(a.business_date) BETWEEN '${param_name1}' AND '${param_name2}'
            OR DATE(a.business_date) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
        )
        AND a.order_type IN ('医美', '退款')
        AND a.category_name <> '异业联盟'
        AND a.channel_name NOT IN ('会员', '10%返券', '12%返券', '15%返券')
        AND a.referrer_name <> '职员'
        AND a.product_name <> '多部位脱毛（会员赠送）'
        AND a.product_name NOT LIKE '%固定组-0%'
        AND a.customer_name NOT LIKE '%员工%'
        AND a.category_level2_name <> '会员权益-虚拟商品'
        AND a.office_name NOT IN ('南京生美', '上海外滩生美')
        AND a.account_item_type NOT IN ('账户权益', '代金券权益')
        AND a.billing_method_name NOT LIKE '%微商城%'
        AND COALESCE(b.level_name, '') <> '股东五折卡'
        AND a.envelope_name NOT LIKE '%积分商城%'
        GROUP BY 
            a.office_name, a.customer_id, a.membership_number, b.level_name, 
            c.order_number, a.customer_name, a.category_level1_name, 
            a.category_level2_name, a.category_level3_name, a.envelope_name,
            a.product_name, a.order_id, a.order_item_id, a.order_line_index, 
            c.unit_retail_price, c.order_quantity, a.business_date, 
            a.billing_method_groupCode, a.order_type
        HAVING 
            -- 排除特定的折扣组合条件
            NOT (zkbl = 70.0 AND level_name LIKE '%银卡会员%' AND category_level3_name = '美国保妥适')
            AND NOT (zkbl = 70.0 AND level_name LIKE '%银卡会员%' AND category_level2_name = '安和颜')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%金卡会员%' AND category_level3_name = '美国保妥适')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%金卡会员%' AND envelope_name LIKE '%M22黄金超光子全模式（面部/颈部）【6支】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%铂金会员%' AND category_level3_name = '美国保妥适')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%铂金会员%' AND envelope_name LIKE '%M22黄金超光子全模式（面部/颈部）【6支】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%黑金会员%' AND category_level3_name = '美国保妥适')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%黑金会员%' AND envelope_name LIKE '%M22黄金超光子全模式（面部/颈部）【6支】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%金卡会员%' AND envelope_name LIKE '%黑金超光子（面部/颈部）【6次】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%铂金会员%' AND envelope_name LIKE '%黑金超光子（面部/颈部）【6次】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%黑金会员%' AND envelope_name LIKE '%黑金超光子（面部/颈部）【6次】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%金卡会员%' AND envelope_name LIKE '%黑金超光子（面部）【6支】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%铂金会员%' AND envelope_name LIKE '%黑金超光子（面部）【6支】%')
            AND NOT (zkbl = 50.0 AND level_name LIKE '%黑金会员%' AND envelope_name LIKE '%黑金超光子（面部）【6支】%')
    ) detail_data
    GROUP BY office_name
),

-- 执行数据CTE：计算当期和同期执行金额
execution_data AS (
    SELECT 
        x0.office_name,
        SUM(CASE WHEN DATE(x0.data_ymd) BETWEEN '${param_name1}' AND '${param_name2}' 
                 THEN x0.zje ELSE 0 END) AS amount_zx_curr,
        SUM(CASE WHEN DATE(x0.data_ymd) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND DATE_SUB('${param_name2}', INTERVAL 1 MONTH)
                 THEN x0.zje ELSE 0 END) AS amount_zx_prev
    FROM static_d_zhixing_base x0
    LEFT JOIN performance_target_by_organization x2 
        ON x2.organization_id = x0.office_id
        AND x2.year_month = DATE_FORMAT(x0.data_ymd, '%Y-%m')
    WHERE DATE(x0.data_ymd) BETWEEN DATE_SUB('${param_name1}', INTERVAL 1 MONTH) AND '${param_name2}'
    GROUP BY x0.office_name
)

-- 主查询：合并所有数据
SELECT 
    COALESCE(cd.office_name, ed.office_name) AS office_name,
    -- 当期现金流数据
    COALESCE(cd.xkzf_curr, 0) AS xkzf_curr,
    COALESCE(cd.zcdk_curr, 0) AS zcdk_curr,
    COALESCE(cd.qyzr_curr, 0) AS qyzr_curr,
    COALESCE(cd.ptjs_curr, 0) AS ptjs_curr,
    COALESCE(cd.sjskje_curr, 0) AS sjskje_curr,
    COALESCE(cd.hjskje_curr, 0) AS hjskje_curr,
    -- 同期现金流数据
    COALESCE(cd.xkzf_prev, 0) AS xkzf_prev,
    COALESCE(cd.zcdk_prev, 0) AS zcdk_prev,
    COALESCE(cd.qyzr_prev, 0) AS qyzr_prev,
    COALESCE(cd.ptjs_prev, 0) AS ptjs_prev,
    COALESCE(cd.sjskje_prev, 0) AS sjskje_prev,
    COALESCE(cd.hjskje_prev, 0) AS hjskje_prev,
    -- 当期和同期执行数据
    COALESCE(ed.amount_zx_curr, 0) AS amount_zx_curr,
    COALESCE(ed.amount_zx_prev, 0) AS amount_zx_prev,
    -- 计算同比增长率
    CASE 
        WHEN COALESCE(cd.hjskje_prev, 0) = 0 THEN NULL
        ELSE ROUND((COALESCE(cd.hjskje_curr, 0) - COALESCE(cd.hjskje_prev, 0)) * 100.0 / COALESCE(cd.hjskje_prev, 0), 2)
    END AS hjskje_growth_rate,
    CASE 
        WHEN COALESCE(ed.amount_zx_prev, 0) = 0 THEN NULL
        ELSE ROUND((COALESCE(ed.amount_zx_curr, 0) - COALESCE(ed.amount_zx_prev, 0)) * 100.0 / COALESCE(ed.amount_zx_prev, 0), 2)
    END AS amount_zx_growth_rate
FROM cash_data cd
FULL OUTER JOIN execution_data ed ON cd.office_name = ed.office_name
ORDER BY COALESCE(cd.office_name, ed.office_name);
