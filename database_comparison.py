#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询对比脚本
对比合并SQL和分开SQL的查询结果
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys

# 数据库连接配置
DB_CONFIG = {
    'host': 'rm-bp135o9085r8yeu7tlo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'zmuser',
    'password': 'Zm&83247778',
    'database': 'ebeauty_shzmyh',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def execute_query(connection, query, description):
    """执行查询并返回结果"""
    try:
        print(f"\n🔍 执行查询: {description}")
        df = pd.read_sql(query, connection)
        print(f"✅ 查询成功，返回 {len(df)} 行数据")
        return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def get_original_query1(param1, param2):
    """原始查询1 - 当期数据"""
    return f"""
    select A.office_name,sum(A.xkzf) xkzf,sum(a.zcdk) zcdk,sum(a.qyzr) qyzr,sum(a.ptjs) ptjs,sum(a.sjskje) sjskje,sum(a.hjskje) hjskje,sum(a.ddyj) ddyj,sum(a.zkje) zkje  
      from (SELECT a.office_name,a.customer_id,a.membership_number,b.level_name,a.customer_name,c.order_number,a.category_level1_name,a.category_level2_name,a.category_level3_name,a.envelope_name,a.product_name
    ,COALESCE(sum(case when a.billing_method_groupCode='现款支付' then a.amount end ),0) xkzf
    ,COALESCE(sum(case when a.billing_method_groupCode='资产抵扣' then a.amount end ),0) zcdk
    ,COALESCE(sum(case when a.billing_method_groupCode='权益折让' then a.amount end ),0) qyzr
    ,COALESCE(sum(case when a.billing_method_groupCode='平台结算' then a.amount end ),0) ptjs
    ,COALESCE(sum(case when a.billing_method_groupCode in ('现款支付','资产抵扣','平台结算') then a.amount end ),0) sjskje
    ,COALESCE(sum(a.amount),0) hjskje
    ,COALESCE(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end,0) ddyj
    ,ROUND(COALESCE(COALESCE(sum(a.amount)*100,0)/(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end),0),1) zkbl
    ,case when ROUND(COALESCE(COALESCE(sum(a.amount)*100,0)/(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end),0),1)<100 then COALESCE(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end,0)-COALESCE(sum(case when a.billing_method_groupCode in ('现款支付','资产抵扣','平台结算') then a.amount end ),0) else 0 end zkje
    FROM temp_middle_cash a left join dim_customer_irn_dd b on a.customer_id=b.customer_id
    left join dws_trd_order_item_irn_dd c on a.order_item_id=c.order_item_id
    where a.business_date>='{param1}'
    and date(a.business_date)<='{param2}'
    and a.order_type in ('医美','退款')
    and a.category_name <>'异业联盟'
    and a.channel_name not in ('会员','10%返券','12%返券','15%返券')
    and a.referrer_name <>'职员'
    and a.product_name<>'多部位脱毛（会员赠送）'
    and a.product_name not like '%固定组-0%'
    and a.customer_name not like '%员工%'
    and a.category_level2_name<>'会员权益-虚拟商品'
    and a.office_name not in ('南京生美','上海外滩生美')
    and a.account_item_type not in ('账户权益','代金券权益')
    and billing_method_name not like '%微商城%'
    and b.level_name<>'股东五折卡'
    and a.envelope_name not like '%积分商城%'
    group by a.office_name,a.customer_id,a.membership_number,b.level_name,c.order_number,a.customer_name,a.category_level1_name,a.category_level2_name,a.category_level3_name,a.envelope_name
    ,a.product_name,a.order_id,a.order_item_id,a.order_line_index,c.unit_retail_price,c.order_quantity) A
    where not (a.zkbl=70.0 and a.level_name like '%银卡会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=70.0 and a.level_name like '%银卡会员%' and a.category_level2_name='安和颜')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    GROUP BY A.office_name
    """

def get_original_query2(param1, param2):
    """原始查询2 - 同期数据"""
    return f"""
    select A.office_name,sum(A.xkzf) xkzf,sum(a.zcdk) zcdk,sum(a.qyzr) qyzr,sum(a.ptjs) ptjs,sum(a.sjskje) sjskje,sum(a.hjskje) hjskje,sum(a.ddyj) ddyj,sum(a.zkje) zkje 
    from (SELECT a.office_name,a.customer_id,a.membership_number,b.level_name,a.customer_name,c.order_number,a.category_level1_name,a.category_level2_name,a.category_level3_name,a.envelope_name,a.product_name
    ,COALESCE(sum(case when a.billing_method_groupCode='现款支付' then a.amount end ),0) xkzf
    ,COALESCE(sum(case when a.billing_method_groupCode='资产抵扣' then a.amount end ),0) zcdk
    ,COALESCE(sum(case when a.billing_method_groupCode='权益折让' then a.amount end ),0) qyzr
    ,COALESCE(sum(case when a.billing_method_groupCode='平台结算' then a.amount end ),0) ptjs
    ,COALESCE(sum(case when a.billing_method_groupCode in ('现款支付','资产抵扣','平台结算') then a.amount end ),0) sjskje
    ,COALESCE(sum(a.amount),0) hjskje
    ,COALESCE(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end,0) ddyj
    ,ROUND(COALESCE(COALESCE(sum(a.amount)*100,0)/(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end),0),1) zkbl
    ,case when ROUND(COALESCE(COALESCE(sum(a.amount)*100,0)/(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end),0),1)<100 then COALESCE(case when a.order_type='退款' then -c.order_quantity*c.unit_retail_price  else c.order_quantity*c.unit_retail_price end,0)-COALESCE(sum(case when a.billing_method_groupCode in ('现款支付','资产抵扣','平台结算') then a.amount end ),0) else 0 end zkje
    FROM temp_middle_cash a left join dim_customer_irn_dd b on a.customer_id=b.customer_id
    left join dws_trd_order_item_irn_dd c on a.order_item_id=c.order_item_id
    where a.business_date>='{param1}'- INTERVAL 1 MONTH
    and date(a.business_date)<='{param2}'- INTERVAL 1 MONTH
    and a.order_type in ('医美','退款')
    and a.category_name <>'异业联盟'
    and a.channel_name not in ('会员','10%返券','12%返券','15%返券')
    and a.referrer_name <>'职员'
    and a.product_name<>'多部位脱毛（会员赠送）'
    and a.product_name not like '%固定组-0%'
    and a.customer_name not like '%员工%'
    and a.category_level2_name<>'会员权益-虚拟商品'
    and a.office_name not in ('南京生美','上海外滩生美')
    and a.account_item_type not in ('账户权益','代金券权益')
    and billing_method_name not like '%微商城%'
    and b.level_name<>'股东五折卡'
    and a.envelope_name not like '%积分商城%'
    group by a.office_name,a.customer_id,a.membership_number,b.level_name,c.order_number,a.customer_name,a.category_level1_name,a.category_level2_name,a.category_level3_name,a.envelope_name
    ,a.product_name,a.order_id,a.order_item_id,a.order_line_index,c.unit_retail_price,c.order_quantity) A
    where not (a.zkbl=70.0 and a.level_name like '%银卡会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=70.0 and a.level_name like '%银卡会员%' and a.category_level2_name='安和颜')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.category_level3_name='美国保妥适')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%M22黄金超光子全模式（面部/颈部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%黑金超光子（面部/颈部）【6次】%')
    and not (a.zkbl=50.0 and a.level_name like '%金卡会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%铂金会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    and not (a.zkbl=50.0 and a.level_name like '%黑金会员%' and a.envelope_name like '%黑金超光子（面部）【6支】%')
    GROUP BY A.office_name
    """

def get_original_query3(param1, param2):
    """原始查询3 - 执行数据"""
    return f"""
    SELECT 
        curr.office_name,
        curr.amount_zx AS amount_zx,
        prev.amount_zx AS amount_zx_sy
    FROM (
        SELECT 
            x0.office_id,
            x0.office_name,
            SUM(x0.zje) AS amount_zx
        FROM static_d_zhixing_base x0
        LEFT JOIN performance_target_by_organization x2 
            ON x2.organization_id = x0.office_id
            AND x2.`year_month` = DATE_FORMAT(x0.data_ymd, '%Y-%m')
        WHERE date(x0.data_ymd) BETWEEN '{param1}' AND '{param2}'
        GROUP BY x0.office_id, x0.office_name
    ) curr
    LEFT JOIN (
        SELECT 
            x0.office_id,
            x0.office_name,
            SUM(x0.zje) AS amount_zx
        FROM static_d_zhixing_base x0
        LEFT JOIN performance_target_by_organization x2 
            ON x2.organization_id = x0.office_id
            AND x2.`year_month` = DATE_FORMAT(x0.data_ymd, '%Y-%m')
        WHERE date(x0.data_ymd )
            BETWEEN DATE_SUB('{param1}', INTERVAL 1 MONTH)
            AND DATE_SUB('{param2}', INTERVAL 1 MONTH)
        GROUP BY x0.office_id, x0.office_name
    ) prev ON curr.office_name = prev.office_name
    """

def main():
    """主函数"""
    print("🚀 开始数据库查询对比")
    print("=" * 50)
    
    # 查询参数
    param1 = '2025-07-01'
    param2 = '2025-07-30'
    
    print(f"📅 查询参数: {param1} 到 {param2}")
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        # 执行原始查询
        print("\n" + "="*50)
        print("📊 执行原始分开的查询")
        print("="*50)
        
        df1 = execute_query(conn, get_original_query1(param1, param2), "原始查询1 - 当期数据")
        df2 = execute_query(conn, get_original_query2(param1, param2), "原始查询2 - 同期数据") 
        df3 = execute_query(conn, get_original_query3(param1, param2), "原始查询3 - 执行数据")
        
        if df1 is not None:
            print(f"\n📋 原始查询1结果预览:")
            print(df1.head())
            print(f"总计: {len(df1)} 行")
            
        if df2 is not None:
            print(f"\n📋 原始查询2结果预览:")
            print(df2.head())
            print(f"总计: {len(df2)} 行")
            
        if df3 is not None:
            print(f"\n📋 原始查询3结果预览:")
            print(df3.head())
            print(f"总计: {len(df3)} 行")
        
        # 读取合并查询
        print("\n" + "="*50)
        print("📊 执行合并优化后的查询")
        print("="*50)
        
        with open('merged_optimized_query.sql', 'r', encoding='utf-8') as f:
            merged_query = f.read()
            # 替换参数
            merged_query = merged_query.replace('${param_name1}', param1)
            merged_query = merged_query.replace('${param_name2}', param2)
        
        df_merged = execute_query(conn, merged_query, "合并优化查询")
        
        if df_merged is not None:
            print(f"\n📋 合并查询结果预览:")
            print(df_merged.head())
            print(f"总计: {len(df_merged)} 行")
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if df1 is not None:
            df1.to_csv(f'original_query1_{timestamp}.csv', index=False, encoding='utf-8-sig')
        if df2 is not None:
            df2.to_csv(f'original_query2_{timestamp}.csv', index=False, encoding='utf-8-sig')
        if df3 is not None:
            df3.to_csv(f'original_query3_{timestamp}.csv', index=False, encoding='utf-8-sig')
        if df_merged is not None:
            df_merged.to_csv(f'merged_query_{timestamp}.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n💾 查询结果已保存到CSV文件 (时间戳: {timestamp})")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
    finally:
        conn.close()
        print("\n🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
